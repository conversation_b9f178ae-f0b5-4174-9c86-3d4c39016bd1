/**
 * @copyright: © 2025, 深圳市泛途智能科技有限公司. All rights reserved.
 * @file: testcase_websocket.c
 * @description: websocekt测试用例文件
 * @author: <PERSON>
 * @date: 2025-07-18
 */
#include <freertos/FreeRTOS.h>
#include "sk_log.h"
#include "sk_config.h"
#include "sk_wifi.h"
#include "sk_websocket.h"
#include "sk_test_common.h"
// === 新增：音频相关头文件 ===
#include "sk_opus.h"           // SkOpusInit
#include "sk_audio.h"          // SkAudioInit, SkPlayerSetCallback, SkSrSetSendFunc
#include "sk_opus_dec.h"       // SkOpusDecFeedPlayAudio, SkOpusDecGetHandler, SkOpusDecPlayRemote
#include "sk_opus_enc.h"       // SkOpusEncEnqueue
#include "sk_sm.h"      

#define SK_WS_TS_BUF_SIZE 2048

bool g_skTcWifiState = false;
bool g_skTcWsConnected = false;
bool g_skTcWsReconnected = false;
bool g_skTcWsDisconnect = false;
bool g_skTcWsBinDataRecv = false;
bool g_skTcWsTxtDataRecv = false;
uint8_t *g_wsTsTxBuf = NULL;
uint8_t *g_wsTsSrcBuf = NULL;
uint8_t *g_wsTsRxBuf = NULL;
size_t g_skTcWsRxDataLen = 0;


SkStateHandler g_smHandler = NULL;  // 测试用例专用状态机句柄



void SkTcStubWifiEvent(uint32_t event) {
    if (event == SK_WIFI_EVENT_STA_CONNECTED) {
        g_skTcWifiState = true;
    }
}

void SkTcStubOnWsEvent(void *arg, uint32_t event) {
    SK_LOGI("TC", "Websocket event %u", event);
    switch (event) {
        case SK_WS_EVENT_CONNECTED:
            if (g_skTcWsConnected) {
                g_skTcWsReconnected = true;
            }
            g_skTcWsConnected = true;
            break;
        case SK_WS_EVENT_DISCONNECTED:
            if (g_skTcWsConnected) {
                g_skTcWsDisconnect = true;
            }
            break;
        default:
            break;
    }

    return;
}

void SkTcStubWsBinDataCallback(void *arg, void *data, uint16_t len) {
    SK_LOGD("TC", "Websocket binary data recv len %u", len);
    memcpy(g_wsTsRxBuf, data, len);
    g_skTcWsRxDataLen = len;
    g_skTcWsBinDataRecv = true;
}

void SkTcStubWsTxtDataCallback(void *arg, void *data, uint16_t len) {
    memcpy(g_wsTsRxBuf, data, len);
    g_skTcWsRxDataLen = len;
    g_skTcWsTxtDataRecv = true;
    g_wsTsRxBuf[len] = '\0';
}

bool SkTcWsConnect() {
    g_skTcWsDisconnect = false;
    SkWsStopConnect();
    if (!SkTcWaitConditionSec(&g_skTcWsDisconnect, 10)) {
        SK_LOGI("TC", "Websocket disconnect timeout");
        return false;
    }
    g_skTcWsConnected = false;
    SkWsStartConnect();
    if (!SkTcWaitConditionSec(&g_skTcWsConnected, 60)) {
        SK_LOGI("TC", "Websocket connect timeout");
        return false;
    }
    g_skTcWsDisconnect = false;

    return true;
}

bool SkTcWsSendSubCase(size_t len, uint8_t flag) {
    uint8_t *outputBuf = g_wsTsTxBuf;
    uint8_t *inputBuf = g_wsTsSrcBuf;
    size_t outLen ;
    uint16_t randData = rand() % 256;
    bool binary = ((flag & SK_WS_PACKET_FLAG_BINARY) != 0);

    if (len > SK_WS_TS_BUF_SIZE) {
        SK_LOGI("TC", "Input data length error");
        return false;
    }

    for (int i = 0; i < len; i++) {
        inputBuf[i] = ((randData + i) % 26) + 'a';
    }

    outLen = SkWsPacketData(outputBuf, SK_WS_TS_BUF_SIZE, inputBuf, len, flag);
    if (outLen == 0) {
        SK_LOGI("TC", "Packet data failed");
        return false;
    }
    if (SkWsSendRaw(outputBuf, outLen) != SK_RET_SUCCESS) {
        SK_LOGI("TC", "Send data failed");
        return false;
    }
    if (binary) {
        if (!SkTcWaitConditionSec(&g_skTcWsBinDataRecv, 60)) {
            SK_LOGI("TC", "Websocket binary data recv timeout");
            return false;
        }
        g_skTcWsBinDataRecv = false;
    } else {
        if (!SkTcWaitConditionSec(&g_skTcWsTxtDataRecv, 60)) {
            SK_LOGI("TC", "Websocket binary data recv timeout");
            return false;
        }
        g_skTcWsTxtDataRecv = false;
    }

    if (g_skTcWsRxDataLen != len) {
        SK_LOGI("TC", "Binary data recv length error");
        return false;
    }
    if (memcmp(g_wsTsRxBuf, g_wsTsSrcBuf, g_skTcWsRxDataLen) != 0) {
        SK_LOGI("TC", "Binary data recv error");
        return false;
    }

    return true;
}

bool SkTcWsSendBinary() {
    uint8_t flag = SK_WS_PACKET_FLAG_BINARY;
    if (!SkWsIsConnected()) {
        SK_LOGI("TC", "Websocket is not connected");
        return false;
    }
    SK_LOGI("TC", "Subcase1: Send binary data 1 byte");
    if (!SkTcWsSendSubCase(1, flag)) {
        SK_LOGI("TC", "Send binary data 1 failed");
        return false;
    }
    SK_LOGI("TC", "Send binary data 1 success");
    if (!SkTcWsSendSubCase(10, flag)) {
        SK_LOGI("TC", "Send binary data 10 failed");
        return false;
    }
    SK_LOGI("TC", "Send binary data 10 success");
    if (!SkTcWsSendSubCase(100, flag)) {
        SK_LOGI("TC", "Send binary data 100 failed");
        return false;
    }
    SK_LOGI("TC", "Send binary data 100 success");
    if (!SkTcWsSendSubCase(125, flag)) {
        SK_LOGI("TC", "Send binary data 125 failed");
        return false;
    }
    SK_LOGI("TC", "Send binary data 125 success");
    if (!SkTcWsSendSubCase(126, flag)) {
        SK_LOGI("TC", "Send binary data 126 failed");
        return false;
    }
    SK_LOGI("TC", "Send binary data 126 success");
    if (!SkTcWsSendSubCase(127, flag)) {
        SK_LOGI("TC", "Send binary data 127 failed");
        return false;
    }
    SK_LOGI("TC", "Send binary data 127 success");
    if (!SkTcWsSendSubCase(512, flag)) {
        SK_LOGI("TC", "Send binary data 512 failed");
        return false;
    }
    SK_LOGI("TC", "Send binary data 512 success");
    if (!SkTcWsSendSubCase(1024, flag)) {
        SK_LOGI("TC", "Send binary data 1024 failed");
        return false;
    }
    SK_LOGI("TC", "Send binary data 1024 success");
    return true;
}

bool SkTcWsSendBinaryAbnormal() {
    size_t outLen;
    uint8_t flag = SK_WS_PACKET_FLAG_BINARY;

    if (!SkWsIsConnected()) {
        SK_LOGI("TC", "Websocket is not connected");
        return false;
    }
    outLen = SkWsPacketData(g_wsTsTxBuf, SK_WS_TS_BUF_SIZE, g_wsTsRxBuf, 1280, flag);
    if (outLen == 0) {
        SK_LOGI("TC", "Packet data failed");
        return false;
    }
    g_skTcWsDisconnect = false;
    if (SkWsSendRaw(g_wsTsTxBuf, outLen) != SK_RET_SUCCESS) {
        SK_LOGI("TC", "Send data failed");
        return false;
    }
    if (!SkTcWaitConditionSec(&g_skTcWsDisconnect, 60)) {
        SK_LOGI("TC", "Websocket disconnect timeout");
        return false;
    }
    if (!SkTcWaitConditionSec(&g_skTcWsReconnected, 60)) {
        SK_LOGI("TC", "Websocket reconnect timeout");
        return false;
    }
    SK_LOGI("TC", "Send/Recv binary data 1280 success");
    return true;
}

bool SkTcWsSendText() {
    uint8_t flag = 0;

    if (!SkWsIsConnected()) {
        SK_LOGI("TC", "Websocket is not connected");
        return false;
    }
    SK_LOGI("TC", "Subcase1: Send txt data 1 byte");
    if (!SkTcWsSendSubCase(1, flag)) {
        SK_LOGI("TC", "Send txt data 1 failed");
        return false;
    }
    SK_LOGI("TC", "Send txt data 1 success");
    if (!SkTcWsSendSubCase(10, flag)) {
        SK_LOGI("TC", "Send txt data 10 failed");
        return false;
    }
    SK_LOGI("TC", "Send txt data 10 success");
    if (!SkTcWsSendSubCase(100, flag)) {
        SK_LOGI("TC", "Send txt data 100 failed");
        return false;
    }
    SK_LOGI("TC", "Send txt data 100 success");
    if (!SkTcWsSendSubCase(125, flag)) {
        SK_LOGI("TC", "Send txt data 125 failed");
        return false;
    }
    SK_LOGI("TC", "Send txt data 125 success");
    if (!SkTcWsSendSubCase(126, flag)) {
        SK_LOGI("TC", "Send txt data 126 failed");
        return false;
    }
    SK_LOGI("TC", "Send txt data 126 success");
    if (!SkTcWsSendSubCase(127, flag)) {
        SK_LOGI("TC", "Send txt data 127 failed");
        return false;
    }
    SK_LOGI("TC", "Send txt data 127 success");
    if (!SkTcWsSendSubCase(512, flag)) {
        SK_LOGI("TC", "Send txt data 512 failed");
        return false;
    }
    SK_LOGI("TC", "Send txt data 512 success");
    if (!SkTcWsSendSubCase(1024, flag)) {
        SK_LOGI("TC", "Send txt data 1024 failed");
        return false;
    }
    SK_LOGI("TC", "Send txt data 1024 success");
    return true;
}

typedef bool (*SkTcFunc)(void);
SkTcFunc SkTcFuncList[] = {
    SkTcWsConnect,
    SkTcWsSendBinary,
    SkTcWsSendText,
    SkTcWsSendBinaryAbnormal,
};

bool SkTsWsInit() {

    SkWsInit();
    SkWsStart();

    SkWsSetServerIp("************", 8766);

    SkWsRegOnEventCallback(SkTcStubOnWsEvent, NULL);
    SkWsRegOnBinDataCallback(SkTcStubWsBinDataCallback, NULL);
    SkWsRegOnTxtDataCallback(SkTcStubWsTxtDataCallback, NULL);
    g_wsTsTxBuf = malloc(SK_WS_TS_BUF_SIZE);
    g_wsTsRxBuf = malloc(SK_WS_TS_BUF_SIZE);
    g_wsTsSrcBuf = malloc(SK_WS_TS_BUF_SIZE);
    if (g_wsTsTxBuf == NULL || g_wsTsRxBuf == NULL || g_wsTsSrcBuf == NULL) {
        SK_LOGI("TS", "Malloc failed");
        return false;
    }

    return true;
}

bool SkTsWsDeinit() {
    SkWsStop();
    if (g_wsTsTxBuf != NULL) {
        free(g_wsTsTxBuf);
        g_wsTsTxBuf = NULL;
    }
    if (g_wsTsRxBuf != NULL) {
        free(g_wsTsRxBuf);
        g_wsTsRxBuf = NULL;
    }
    if (g_wsTsSrcBuf != NULL) {
        free(g_wsTsSrcBuf);
        g_wsTsSrcBuf = NULL;
    }
    vTaskDelay(pdMS_TO_TICKS(100));
    SkWsDeinit();
    return true;
}

bool SkTcWsInit() {
    g_skTcWifiState = false;
    g_skTcWsConnected = false;
    g_skTcWsReconnected = false;
    g_skTcWsDisconnect = false;
    g_skTcWsBinDataRecv = false;
    g_skTcWsTxtDataRecv = false;
    g_skTcWsRxDataLen = 0;
    SkWsStartConnect();
    if (!SkTcWaitConditionSec(&g_skTcWsConnected, 60)) {
        SK_LOGI("TC", "Websocket connect timeout");
        return false;
    }
    return true;
}

bool SkTcWsDeinit() {
    SkWsStopConnect();
    if (!SkTcWaitConditionSec(&g_skTcWsDisconnect, 10)) {
        SK_LOGI("TC", "Websocket disconnect timeout");
        return false;
    }
    return true;
}

void SkTestWebSocketMain() {
    uint32_t i = 0;

    SkConfigInit();
    SkWifiInit();
    SkWifiRegEventCb(SkTcStubWifiEvent);
    SkWifiStartSta();

    if (!SkTcWaitConditionSec(&g_skTcWifiState, 60)) {
        SK_LOGI("TC", "Wifi connect timeout");
        return;
    }

    // === 新增：音频初始化（SkBspBoardInit已经完成）===
    SK_LOGI("TC", "Starting audio initialization...");
    
    g_smHandler = SkSmInit();

    // 1. Opus编解码器初始化
    SkOpusInit(16000, 1, 60);
    SK_LOGI("TC", "✓ Opus codec initialized");
    
    // 2. 音频系统初始化
    SkAudioInit(sizeof(uint16_t), 960);
    SK_LOGI("TC", "✓ Audio system initialized");
    
    // 3. 设置音频回调
    SkPlayerSetCallback(SkOpusDecFeedPlayAudio);
    SkSrSetSendFunc(SkOpusEncEnqueue);
    SK_LOGI("TC", "✓ Audio callbacks configured");
    
    // 4. 发送系统初始化完成事件 - 触发自动播放
    SkSmSendEvent(g_smHandler, SM_EVENT_SYSTEM, SM_EVENT_SYSTEM_INIT_OK, 0, 0);

    if (!SkTsWsInit()) {
        SK_LOGI("TC", "Test suit init failed");
        return;
    }
    for (i = 0; i < sizeof(SkTcFuncList) / sizeof(SkTcFuncList[0]); i++) {
        SK_LOGI("TC", "Test case %d init...", i);
        if (!SkTcWsInit()) {
            SK_LOGI("TC", "Test case %d init failed", i);
            return;
        }
        bool result = SkTcFuncList[i]();
        if (!result) {
            SK_LOGI("TC", "Test case %d failed", i);
            return;
        } else {
            SK_LOGI("TC", "Test case %d passed", i);
        }
        SK_LOGI("TC", "Test case %d deinit...", i);
        if (!SkTcWsDeinit()) {
            SK_LOGI("TC", "Test case %d deinit failed", i);
            return;
        }
    }
    SkTsWsDeinit();
}